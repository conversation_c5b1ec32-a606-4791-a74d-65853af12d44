<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为你画一颗爱心 💕</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Noto+Sans+SC:wght@300;400;500&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            height: 100vh;
            background: linear-gradient(135deg, 
                #ffeef8 0%, 
                #ffe0f0 25%, 
                #ffd1e8 50%, 
                #ffb3d9 75%, 
                #ff9ecf 100%);
            overflow: hidden;
            font-family: 'Noto Sans SC', sans-serif;
            position: relative;
        }
        
        /* 背景光斑效果 */
        .background-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 30%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 70%, rgba(255, 105, 180, 0.2) 0%, transparent 50%),
                        radial-gradient(circle at 50% 50%, rgba(255, 20, 147, 0.1) 0%, transparent 50%);
            animation: glowPulse 6s ease-in-out infinite alternate;
        }
        
        @keyframes glowPulse {
            0% { opacity: 0.5; transform: scale(1); }
            100% { opacity: 0.8; transform: scale(1.1); }
        }
        
        /* 主容器 */
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            position: relative;
            z-index: 10;
        }
        
        /* 标题样式 */
        .title {
            font-family: 'Dancing Script', cursive;
            font-size: 3rem;
            color: #d63384;
            text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.3);
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeInTitle 2s ease-out 0.5s forwards;
        }
        
        @keyframes fadeInTitle {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* SVG容器 */
        .heart-container {
            position: relative;
            margin: 2rem 0;
        }
        
        .heart-svg {
            width: 300px;
            height: 300px;
            filter: drop-shadow(0 0 20px rgba(255, 105, 180, 0.5));
        }
        
        /* 爱心路径样式 */
        .heart-path {
            fill: none;
            stroke: #ff1493;
            stroke-width: 4;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawHeart 4s ease-in-out 2s forwards;
        }
        
        @keyframes drawHeart {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        /* 爱心填充动画 */
        .heart-fill {
            fill: url(#heartGradient);
            opacity: 0;
            animation: fillHeart 2s ease-in-out 6s forwards;
        }
        
        @keyframes fillHeart {
            to {
                opacity: 1;
            }
        }
        
        /* 光环效果 */
        .heart-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 350px;
            height: 350px;
            border-radius: 50%;
            background: radial-gradient(circle, transparent 40%, rgba(255, 20, 147, 0.1) 50%, transparent 70%);
            opacity: 0;
            animation: glowEffect 3s ease-in-out 7s infinite;
        }
        
        @keyframes glowEffect {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
        }
        
        /* 粒子效果 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            font-size: 1rem;
            animation: float 8s linear infinite;
            opacity: 0.7;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
        
        /* 消息文本 */
        .message {
            font-size: 1.5rem;
            color: #d63384;
            text-align: center;
            margin-top: 2rem;
            opacity: 0;
            animation: fadeInMessage 2s ease-out 8s forwards;
            font-weight: 300;
            letter-spacing: 1px;
        }
        
        @keyframes fadeInMessage {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 按钮样式 */
        .replay-btn {
            margin-top: 2rem;
            padding: 12px 30px;
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-family: 'Noto Sans SC', sans-serif;
            cursor: pointer;
            opacity: 0;
            animation: fadeInButton 2s ease-out 9s forwards;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
        }
        
        .replay-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
        }
        
        @keyframes fadeInButton {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="background-glow"></div>
    
    <div class="container">
        <h1 class="title">为你画一颗爱心</h1>
        
        <div class="heart-container">
            <div class="heart-glow"></div>
            <svg class="heart-svg" viewBox="0 0 300 300">
                <defs>
                    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff69b4;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#ff1493;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#dc143c;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- 爱心路径 -->
                <path class="heart-fill" d="M150,250 C150,250 50,180 50,120 C50,80 80,50 120,50 C135,50 150,60 150,60 C150,60 165,50 180,50 C220,50 250,80 250,120 C250,180 150,250 150,250 Z"/>
                <path class="heart-path" d="M150,250 C150,250 50,180 50,120 C50,80 80,50 120,50 C135,50 150,60 150,60 C150,60 165,50 180,50 C220,50 250,80 250,120 C250,180 150,250 150,250 Z"/>
            </svg>
        </div>
        
        <p class="message">每一笔都是我对你的爱 💕</p>
        
        <button class="replay-btn" onclick="replayAnimation()">再看一遍</button>
    </div>
    
    <div class="particles" id="particles"></div>
    
    <script>
        // 创建粒子效果
        function createParticles() {
            const particles = document.getElementById('particles');
            const particleSymbols = ['💖', '✨', '💕', '🌸', '💫', '🌹', '💗', '⭐'];
            
            setInterval(() => {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.innerHTML = particleSymbols[Math.floor(Math.random() * particleSymbols.length)];
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDuration = (Math.random() * 3 + 5) + 's';
                particle.style.animationDelay = Math.random() * 2 + 's';
                
                particles.appendChild(particle);
                
                // 清理粒子
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 8000);
            }, 800);
        }
        
        // 重播动画
        function replayAnimation() {
            location.reload();
        }
        
        // 页面加载完成后开始粒子效果
        window.addEventListener('load', () => {
            createParticles();
        });
    </script>
</body>
</html>
